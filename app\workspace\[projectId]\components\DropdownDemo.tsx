"use client";

import React, { useState } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger,
} from "@/components/ui/context-menu";
import { DropdownMenu } from "./DropdownMenu";
import { 
  Settings, 
  Palette, 
  Layout, 
  Type, 
  Copy, 
  Trash2, 
  Edit, 
  Plus,
  Download,
  Upload,
  Share,
  Eye,
  EyeOff
} from "lucide-react";

export function DropdownDemo() {
  const [selectedTheme, setSelectedTheme] = useState("");
  const [selectedLayout, setSelectedLayout] = useState("");
  const [selectedFont, setSelectedFont] = useState("");

  // Demo data for different dropdown types
  const themeOptions = [
    { value: "light", label: "Light Theme", icon: <Eye className="w-4 h-4" /> },
    { value: "dark", label: "Dark Theme", icon: <EyeOff className="w-4 h-4" /> },
    { value: "auto", label: "Auto Theme", icon: <Settings className="w-4 h-4" /> },
    { value: "custom", label: "Custom Theme", icon: <Palette className="w-4 h-4" /> },
  ];

  const layoutOptions = [
    { value: "grid", label: "Grid Layout", icon: <Layout className="w-4 h-4" /> },
    { value: "flex", label: "Flex Layout", icon: <Layout className="w-4 h-4" /> },
    { value: "absolute", label: "Absolute Layout", icon: <Layout className="w-4 h-4" /> },
    { value: "relative", label: "Relative Layout", icon: <Layout className="w-4 h-4" /> },
  ];

  const fontOptions = [
    { value: "inter", label: "Inter", icon: <Type className="w-4 h-4" /> },
    { value: "roboto", label: "Roboto", icon: <Type className="w-4 h-4" /> },
    { value: "opensans", label: "Open Sans", icon: <Type className="w-4 h-4" /> },
    { value: "poppins", label: "Poppins", icon: <Type className="w-4 h-4" /> },
  ];

  return (
    <div className="p-6 space-y-8 bg-white rounded-lg border border-gray-200">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Component Dropdown Functionality Demo
        </h2>
        <p className="text-gray-600">
          Explore various dropdown implementations for the component builder
        </p>
      </div>

      {/* Shadcn/ui Select Components */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
          <Settings className="w-5 h-5" />
          Shadcn/ui Select Dropdowns
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Component Type
            </label>
            <Select>
              <SelectTrigger>
                <SelectValue placeholder="Select component type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="button">Button</SelectItem>
                <SelectItem value="input">Input</SelectItem>
                <SelectItem value="card">Card</SelectItem>
                <SelectItem value="modal">Modal</SelectItem>
                <SelectItem value="table">Table</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Size Variant
            </label>
            <Select>
              <SelectTrigger>
                <SelectValue placeholder="Select size" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="xs">Extra Small</SelectItem>
                <SelectItem value="sm">Small</SelectItem>
                <SelectItem value="md">Medium</SelectItem>
                <SelectItem value="lg">Large</SelectItem>
                <SelectItem value="xl">Extra Large</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Color Scheme
            </label>
            <Select>
              <SelectTrigger>
                <SelectValue placeholder="Select color" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="primary">Primary Blue</SelectItem>
                <SelectItem value="secondary">Secondary Gray</SelectItem>
                <SelectItem value="success">Success Green</SelectItem>
                <SelectItem value="warning">Warning Yellow</SelectItem>
                <SelectItem value="danger">Danger Red</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* Custom Dropdown Components */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
          <Palette className="w-5 h-5" />
          Custom Dropdown Components
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Theme Selection
            </label>
            <DropdownMenu
              options={themeOptions}
              value={selectedTheme}
              onSelect={setSelectedTheme}
              placeholder="Choose theme"
            />
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Layout Type
            </label>
            <DropdownMenu
              options={layoutOptions}
              value={selectedLayout}
              onSelect={setSelectedLayout}
              placeholder="Choose layout"
              variant="outline"
            />
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Font Family
            </label>
            <DropdownMenu
              options={fontOptions}
              value={selectedFont}
              onSelect={setSelectedFont}
              placeholder="Choose font"
              size="sm"
            />
          </div>
        </div>
      </div>

      {/* Context Menu Dropdowns */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
          <Copy className="w-5 h-5" />
          Context Menu Dropdowns
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <ContextMenu>
            <ContextMenuTrigger>
              <div className="p-6 border-2 border-dashed border-gray-300 rounded-lg text-center cursor-pointer hover:border-gray-400 transition-colors">
                <div className="text-gray-600 mb-2">
                  <Layout className="w-8 h-8 mx-auto" />
                </div>
                <p className="text-sm text-gray-600">
                  Right-click for component actions
                </p>
              </div>
            </ContextMenuTrigger>
            <ContextMenuContent>
              <ContextMenuItem>
                <Edit className="w-4 h-4 mr-2" />
                Edit Component
              </ContextMenuItem>
              <ContextMenuItem>
                <Copy className="w-4 h-4 mr-2" />
                Duplicate
              </ContextMenuItem>
              <ContextMenuItem>
                <Download className="w-4 h-4 mr-2" />
                Export
              </ContextMenuItem>
              <ContextMenuItem>
                <Share className="w-4 h-4 mr-2" />
                Share
              </ContextMenuItem>
              <ContextMenuItem className="text-red-600">
                <Trash2 className="w-4 h-4 mr-2" />
                Delete
              </ContextMenuItem>
            </ContextMenuContent>
          </ContextMenu>

          <ContextMenu>
            <ContextMenuTrigger>
              <div className="p-6 bg-blue-50 border border-blue-200 rounded-lg text-center cursor-pointer hover:bg-blue-100 transition-colors">
                <div className="text-blue-600 mb-2">
                  <Plus className="w-8 h-8 mx-auto" />
                </div>
                <p className="text-sm text-blue-600">
                  Right-click for creation options
                </p>
              </div>
            </ContextMenuTrigger>
            <ContextMenuContent>
              <ContextMenuItem>
                <Plus className="w-4 h-4 mr-2" />
                Add Button
              </ContextMenuItem>
              <ContextMenuItem>
                <Plus className="w-4 h-4 mr-2" />
                Add Input
              </ContextMenuItem>
              <ContextMenuItem>
                <Plus className="w-4 h-4 mr-2" />
                Add Card
              </ContextMenuItem>
              <ContextMenuItem>
                <Upload className="w-4 h-4 mr-2" />
                Import Component
              </ContextMenuItem>
            </ContextMenuContent>
          </ContextMenu>
        </div>
      </div>

      {/* Selection Summary */}
      {(selectedTheme || selectedLayout || selectedFont) && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-800">
            Current Selections
          </h3>
          <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              {selectedTheme && (
                <div>
                  <span className="font-medium text-gray-700">Theme:</span>
                  <span className="ml-2 text-gray-600">
                    {themeOptions.find(opt => opt.value === selectedTheme)?.label}
                  </span>
                </div>
              )}
              {selectedLayout && (
                <div>
                  <span className="font-medium text-gray-700">Layout:</span>
                  <span className="ml-2 text-gray-600">
                    {layoutOptions.find(opt => opt.value === selectedLayout)?.label}
                  </span>
                </div>
              )}
              {selectedFont && (
                <div>
                  <span className="font-medium text-gray-700">Font:</span>
                  <span className="ml-2 text-gray-600">
                    {fontOptions.find(opt => opt.value === selectedFont)?.label}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
