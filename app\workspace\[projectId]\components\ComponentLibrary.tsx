"use client";

import React, { useState } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger,
} from "@/components/ui/context-menu";
import { Plus, Settings, Copy, Trash2, Search } from "lucide-react";

// Component categories and data
const componentCategories = [
  { value: "all", label: "All Components" },
  { value: "layout", label: "Layout" },
  { value: "forms", label: "Forms" },
  { value: "navigation", label: "Navigation" },
  { value: "feedback", label: "Feedback" },
  { value: "data-display", label: "Data Display" },
];

const componentLibrary = {
  layout: [
    { id: "card", name: "Card", description: "Content container", icon: "📄" },
    { id: "container", name: "Container", description: "Layout wrapper", icon: "📦" },
    { id: "grid", name: "Grid", description: "Grid layout system", icon: "⚏" },
    { id: "flex", name: "Flex", description: "Flexible layout", icon: "📐" },
  ],
  forms: [
    { id: "button", name: "Button", description: "Interactive button component", icon: "🔘" },
    { id: "input", name: "Input", description: "Text input field", icon: "📝" },
    { id: "select", name: "Select", description: "Dropdown selection", icon: "📋" },
    { id: "checkbox", name: "Checkbox", description: "Checkbox input", icon: "☑️" },
    { id: "radio", name: "Radio", description: "Radio button input", icon: "🔘" },
    { id: "textarea", name: "Textarea", description: "Multi-line text input", icon: "📄" },
  ],
  navigation: [
    { id: "navbar", name: "Navbar", description: "Navigation bar", icon: "🧭" },
    { id: "breadcrumb", name: "Breadcrumb", description: "Navigation breadcrumb", icon: "🍞" },
    { id: "tabs", name: "Tabs", description: "Tab navigation", icon: "📑" },
    { id: "sidebar", name: "Sidebar", description: "Side navigation", icon: "📂" },
  ],
  feedback: [
    { id: "alert", name: "Alert", description: "Alert message", icon: "⚠️" },
    { id: "toast", name: "Toast", description: "Toast notification", icon: "🍞" },
    { id: "modal", name: "Modal", description: "Modal dialog", icon: "🪟" },
    { id: "tooltip", name: "Tooltip", description: "Hover tooltip", icon: "💬" },
  ],
  "data-display": [
    { id: "table", name: "Table", description: "Data table", icon: "📊" },
    { id: "list", name: "List", description: "List component", icon: "📝" },
    { id: "badge", name: "Badge", description: "Status badge", icon: "🏷️" },
    { id: "avatar", name: "Avatar", description: "User avatar", icon: "👤" },
  ],
};

interface ComponentLibraryProps {
  onComponentSelect?: (componentId: string) => void;
  onComponentAction?: (action: string, componentId: string) => void;
}

export function ComponentLibrary({ onComponentSelect, onComponentAction }: ComponentLibraryProps) {
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");

  // Helper functions
  const getFilteredComponents = () => {
    let components = selectedCategory === "all" 
      ? Object.values(componentLibrary).flat()
      : componentLibrary[selectedCategory as keyof typeof componentLibrary] || [];
    
    if (searchQuery) {
      components = components.filter(component =>
        component.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        component.description.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }
    
    return components;
  };

  const handleComponentAction = (action: string, componentId: string) => {
    if (onComponentAction) {
      onComponentAction(action, componentId);
    }
    
    if (action === "add" && onComponentSelect) {
      onComponentSelect(componentId);
    }
  };

  return (
    <aside className="w-64 bg-white border-r border-gray-200 overflow-y-auto">
      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-medium">Components</h2>
          <button 
            className="p-1 hover:bg-gray-100 rounded"
            onClick={() => handleComponentAction("add", "new")}
          >
            <Plus className="w-4 h-4 text-gray-600" />
          </button>
        </div>
        
        {/* Search Bar */}
        <div className="mb-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search components..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>
        
        {/* Category Filter Dropdown */}
        <div className="mb-4">
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select category" />
            </SelectTrigger>
            <SelectContent>
              {componentCategories.map((category) => (
                <SelectItem key={category.value} value={category.value}>
                  {category.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        {/* Component List */}
        <div className="space-y-2">
          {getFilteredComponents().length > 0 ? (
            getFilteredComponents().map((component) => (
              <ContextMenu key={component.id}>
                <ContextMenuTrigger>
                  <div 
                    className="p-3 border border-gray-200 rounded-md cursor-pointer hover:bg-gray-50 transition-colors"
                    onClick={() => handleComponentAction("add", component.id)}
                  >
                    <div className="flex items-center gap-2 mb-1">
                      <span className="text-lg">{component.icon}</span>
                      <div className="font-medium">{component.name}</div>
                    </div>
                    <div className="text-sm text-gray-500">{component.description}</div>
                  </div>
                </ContextMenuTrigger>
                <ContextMenuContent>
                  <ContextMenuItem onClick={() => handleComponentAction("add", component.id)}>
                    <Plus className="w-4 h-4 mr-2" />
                    Add to Canvas
                  </ContextMenuItem>
                  <ContextMenuItem onClick={() => handleComponentAction("copy", component.id)}>
                    <Copy className="w-4 h-4 mr-2" />
                    Duplicate
                  </ContextMenuItem>
                  <ContextMenuItem onClick={() => handleComponentAction("settings", component.id)}>
                    <Settings className="w-4 h-4 mr-2" />
                    Settings
                  </ContextMenuItem>
                  <ContextMenuItem 
                    onClick={() => handleComponentAction("delete", component.id)}
                    className="text-red-600"
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    Remove
                  </ContextMenuItem>
                </ContextMenuContent>
              </ContextMenu>
            ))
          ) : (
            <div className="text-center py-8">
              <div className="text-gray-400 mb-2">
                <Search className="w-8 h-8 mx-auto" />
              </div>
              <p className="text-sm text-gray-500">
                No components found
              </p>
            </div>
          )}
        </div>
      </div>
    </aside>
  );
}
