"use client";

import React, { useState, useRef, useEffect } from "react";
import { ChevronDown } from "lucide-react";

interface DropdownOption {
  value: string;
  label: string;
  icon?: React.ReactNode;
  disabled?: boolean;
}

interface DropdownMenuProps {
  options: DropdownOption[];
  value?: string;
  placeholder?: string;
  onSelect?: (value: string) => void;
  className?: string;
  disabled?: boolean;
  variant?: "default" | "outline" | "ghost";
  size?: "sm" | "md" | "lg";
}

export function DropdownMenu({
  options,
  value,
  placeholder = "Select option",
  onSelect,
  className = "",
  disabled = false,
  variant = "default",
  size = "md",
}: DropdownMenuProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedValue, setSelectedValue] = useState(value || "");
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Update selected value when prop changes
  useEffect(() => {
    if (value !== undefined) {
      setSelectedValue(value);
    }
  }, [value]);

  const handleSelect = (optionValue: string) => {
    setSelectedValue(optionValue);
    setIsOpen(false);
    if (onSelect) {
      onSelect(optionValue);
    }
  };

  const selectedOption = options.find(option => option.value === selectedValue);

  const getVariantClasses = () => {
    switch (variant) {
      case "outline":
        return "border border-gray-300 bg-white hover:bg-gray-50";
      case "ghost":
        return "border-0 bg-transparent hover:bg-gray-100";
      default:
        return "border border-gray-300 bg-white hover:bg-gray-50";
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case "sm":
        return "px-2 py-1 text-sm";
      case "lg":
        return "px-4 py-3 text-lg";
      default:
        return "px-3 py-2 text-base";
    }
  };

  return (
    <div className={`relative inline-block text-left ${className}`} ref={dropdownRef}>
      <button
        type="button"
        className={`
          inline-flex items-center justify-between w-full rounded-md
          ${getVariantClasses()}
          ${getSizeClasses()}
          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent
          ${disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer"}
          transition-colors duration-200
        `}
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
        aria-haspopup="true"
        aria-expanded={isOpen}
      >
        <div className="flex items-center gap-2">
          {selectedOption?.icon && (
            <span className="flex-shrink-0">{selectedOption.icon}</span>
          )}
          <span className="truncate">
            {selectedOption ? selectedOption.label : placeholder}
          </span>
        </div>
        <ChevronDown 
          className={`w-4 h-4 transition-transform duration-200 ${
            isOpen ? "transform rotate-180" : ""
          }`} 
        />
      </button>

      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg">
          <div className="py-1 max-h-60 overflow-auto">
            {options.map((option) => (
              <button
                key={option.value}
                className={`
                  w-full px-3 py-2 text-left flex items-center gap-2
                  ${option.disabled 
                    ? "text-gray-400 cursor-not-allowed" 
                    : "text-gray-900 hover:bg-gray-100 cursor-pointer"
                  }
                  ${selectedValue === option.value ? "bg-blue-50 text-blue-900" : ""}
                  transition-colors duration-150
                `}
                onClick={() => !option.disabled && handleSelect(option.value)}
                disabled={option.disabled}
              >
                {option.icon && (
                  <span className="flex-shrink-0">{option.icon}</span>
                )}
                <span className="truncate">{option.label}</span>
                {selectedValue === option.value && (
                  <span className="ml-auto text-blue-600">✓</span>
                )}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

// Example usage component
export function DropdownMenuExample() {
  const [selectedValue, setSelectedValue] = useState("");

  const options = [
    { value: "option1", label: "Option 1", icon: "🔥" },
    { value: "option2", label: "Option 2", icon: "⭐" },
    { value: "option3", label: "Option 3", icon: "🚀" },
    { value: "option4", label: "Disabled Option", icon: "❌", disabled: true },
  ];

  return (
    <div className="p-4 space-y-4">
      <h3 className="text-lg font-medium">Dropdown Menu Examples</h3>
      
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">
          Default Dropdown
        </label>
        <DropdownMenu
          options={options}
          value={selectedValue}
          onSelect={setSelectedValue}
          placeholder="Choose an option"
        />
      </div>

      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">
          Small Outline Dropdown
        </label>
        <DropdownMenu
          options={options}
          variant="outline"
          size="sm"
          placeholder="Small dropdown"
        />
      </div>

      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">
          Large Ghost Dropdown
        </label>
        <DropdownMenu
          options={options}
          variant="ghost"
          size="lg"
          placeholder="Large ghost dropdown"
        />
      </div>

      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">
          Disabled Dropdown
        </label>
        <DropdownMenu
          options={options}
          disabled
          placeholder="Disabled dropdown"
        />
      </div>

      {selectedValue && (
        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
          <p className="text-sm text-blue-900">
            Selected value: <strong>{selectedValue}</strong>
          </p>
        </div>
      )}
    </div>
  );
}
