"use client";

import React, { useState } from "react";
import { useParams } from "next/navigation";
import { Id } from "@/convex/_generated/dataModel";
import { projectsHooks } from "@/hooks/project";
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger,
} from "@/components/ui/context-menu";
import { Plus, Settings, Copy, MoreVertical, Save, Eye, Code } from "lucide-react";
import { ComponentLibrary } from "./components/ComponentLibrary";
import { PropertiesPanel } from "./components/PropertiesPanel";
import { DropdownDemo } from "./components/DropdownDemo";

export default function ComponentBuilderPage() {
  const params = useParams();
  const projectId = params.projectId as Id<"projects">;
  const { getProjectById } = projectsHooks();

  const project = getProjectById(projectId);

  // Type definition for component properties
  interface ComponentProperties {
    // Layout properties
    display?: string;
    width?: string;
    height?: string;

    // Styling properties
    backgroundColor?: string;
    borderRadius?: string;
    shadow?: string;
    border?: string;

    // Typography properties
    fontSize?: string;
    fontWeight?: string;
    textAlign?: string;

    // Additional properties can be added as needed
    [key: string]: any;
  }

  // State for dropdown functionality
  const [selectedComponent, setSelectedComponent] = useState<string | null>(null);
  const [componentProperties, setComponentProperties] = useState<ComponentProperties>({});
  const [showDemo, setShowDemo] = useState(false);

  // Handler functions
  const handleComponentSelect = (componentId: string) => {
    setSelectedComponent(componentId);
    console.log(`Selected component: ${componentId}`);
  };

  const handleComponentAction = (action: string, componentId: string) => {
    switch (action) {
      case "add":
        console.log(`Adding component: ${componentId}`);
        setSelectedComponent(componentId);
        // TODO: Implement component addition to canvas
        break;
      case "copy":
        console.log(`Copying component: ${componentId}`);
        // TODO: Implement component copying
        break;
      case "settings":
        console.log(`Opening settings for component: ${componentId}`);
        setSelectedComponent(componentId);
        break;
      case "delete":
        console.log(`Deleting component: ${componentId}`);
        // TODO: Implement component deletion
        break;
    }
  };

  const handlePropertyChange = (property: string, value: any) => {
    setComponentProperties((prev: ComponentProperties) => ({
      ...prev,
      [property]: value
    }));
    console.log(`Property changed: ${property} = ${value}`);
  };

  if (!project) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-semibold mb-2">Project not found</h1>
          <p className="text-gray-600">The project you're looking for doesn't exist.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-xl font-semibold text-gray-900">{project.name}</h1>
            <p className="text-sm text-gray-600">{project.description}</p>
          </div>
          <div className="flex items-center space-x-4">
            <button className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
              <Save className="w-4 h-4 mr-2" />
              Save
            </button>
            <button className="flex items-center px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">
              <Eye className="w-4 h-4 mr-2" />
              Preview
            </button>
            <button
              className={`flex items-center px-4 py-2 border rounded-md transition-colors ${
                showDemo
                  ? "bg-blue-600 text-white border-blue-600 hover:bg-blue-700"
                  : "border-gray-300 hover:bg-gray-50"
              }`}
              onClick={() => setShowDemo(!showDemo)}
            >
              <Code className="w-4 h-4 mr-2" />
              {showDemo ? "Hide Demo" : "Show Demo"}
            </button>

            {/* More Actions Dropdown */}
            <ContextMenu>
              <ContextMenuTrigger>
                <button className="p-2 border border-gray-300 rounded-md hover:bg-gray-50">
                  <MoreVertical className="w-4 h-4" />
                </button>
              </ContextMenuTrigger>
              <ContextMenuContent>
                <ContextMenuItem>
                  <Copy className="w-4 h-4 mr-2" />
                  Export Component
                </ContextMenuItem>
                <ContextMenuItem>
                  <Settings className="w-4 h-4 mr-2" />
                  Project Settings
                </ContextMenuItem>
                <ContextMenuItem>
                  <Plus className="w-4 h-4 mr-2" />
                  Import Component
                </ContextMenuItem>
              </ContextMenuContent>
            </ContextMenu>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="flex h-[calc(100vh-73px)]">
        {/* Sidebar - Component Library */}
        <ComponentLibrary
          onComponentSelect={handleComponentSelect}
          onComponentAction={handleComponentAction}
        />

        {/* Canvas Area */}
        <main className="flex-1 bg-gray-100 p-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 h-full overflow-auto">
            {showDemo ? (
              <div className="p-6">
                <DropdownDemo />
              </div>
            ) : (
              <div className="p-8 h-full flex items-center justify-center">
                <div className="text-center">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Start Building Your Component
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Drag components from the sidebar to start designing, or click "Show Demo" to see dropdown functionality
                  </p>
                  <div className="w-16 h-16 bg-gray-200 rounded-lg mx-auto"></div>
                </div>
              </div>
            )}
          </div>
        </main>

        {/* Properties Panel */}
        <PropertiesPanel
          selectedComponent={selectedComponent}
          componentData={componentProperties}
          onPropertyChange={handlePropertyChange}
        />
      </div>
    </div>
  );
}