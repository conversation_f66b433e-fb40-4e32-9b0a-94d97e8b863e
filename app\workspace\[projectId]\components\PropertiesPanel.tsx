"use client";

import React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Settings, Palette, Layout, Type } from "lucide-react";

interface PropertiesPanelProps {
  selectedComponent?: string | null;
  componentData?: any;
  onPropertyChange?: (property: string, value: any) => void;
}

export function PropertiesPanel({ 
  selectedComponent, 
  componentData, 
  onPropertyChange 
}: PropertiesPanelProps) {
  
  const handlePropertyChange = (property: string, value: any) => {
    if (onPropertyChange) {
      onPropertyChange(property, value);
    }
  };

  const getComponentName = (componentId: string) => {
    const componentNames: { [key: string]: string } = {
      button: "Button",
      input: "Input",
      card: "Card",
      container: "Container",
      grid: "Grid",
      select: "Select",
      checkbox: "Checkbox",
      navbar: "Navbar",
      alert: "Alert",
      table: "Table",
    };
    return componentNames[componentId] || "Component";
  };

  if (!selectedComponent) {
    return (
      <aside className="w-64 bg-white border-l border-gray-200 overflow-y-auto">
        <div className="p-4">
          <h2 className="text-lg font-medium mb-4">Properties</h2>
          <div className="text-center py-8">
            <div className="text-gray-400 mb-2">
              <Settings className="w-8 h-8 mx-auto" />
            </div>
            <p className="text-sm text-gray-500">
              Select a component to edit its properties
            </p>
          </div>
        </div>
      </aside>
    );
  }

  return (
    <aside className="w-64 bg-white border-l border-gray-200 overflow-y-auto">
      <div className="p-4">
        <h2 className="text-lg font-medium mb-4">Properties</h2>
        
        <div className="space-y-4">
          {/* Component Info */}
          <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
            <div className="text-sm font-medium text-blue-900">
              {getComponentName(selectedComponent)} Selected
            </div>
          </div>
          
          {/* Layout Properties */}
          <div>
            <div className="flex items-center gap-2 mb-3">
              <Layout className="w-4 h-4 text-gray-600" />
              <h3 className="text-sm font-medium text-gray-700">Layout</h3>
            </div>
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Display
                </label>
                <Select 
                  defaultValue="block"
                  onValueChange={(value) => handlePropertyChange("display", value)}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="block">Block</SelectItem>
                    <SelectItem value="inline">Inline</SelectItem>
                    <SelectItem value="flex">Flex</SelectItem>
                    <SelectItem value="grid">Grid</SelectItem>
                    <SelectItem value="inline-flex">Inline Flex</SelectItem>
                    <SelectItem value="none">None</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Width
                </label>
                <Select 
                  defaultValue="auto"
                  onValueChange={(value) => handlePropertyChange("width", value)}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="auto">Auto</SelectItem>
                    <SelectItem value="full">Full (100%)</SelectItem>
                    <SelectItem value="1/2">Half (50%)</SelectItem>
                    <SelectItem value="1/3">Third (33%)</SelectItem>
                    <SelectItem value="1/4">Quarter (25%)</SelectItem>
                    <SelectItem value="fit">Fit Content</SelectItem>
                    <SelectItem value="custom">Custom</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Height
                </label>
                <Select 
                  defaultValue="auto"
                  onValueChange={(value) => handlePropertyChange("height", value)}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="auto">Auto</SelectItem>
                    <SelectItem value="screen">Screen Height</SelectItem>
                    <SelectItem value="1/2">Half Screen</SelectItem>
                    <SelectItem value="1/3">Third Screen</SelectItem>
                    <SelectItem value="fit">Fit Content</SelectItem>
                    <SelectItem value="custom">Custom</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Position
                </label>
                <Select 
                  defaultValue="static"
                  onValueChange={(value) => handlePropertyChange("position", value)}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="static">Static</SelectItem>
                    <SelectItem value="relative">Relative</SelectItem>
                    <SelectItem value="absolute">Absolute</SelectItem>
                    <SelectItem value="fixed">Fixed</SelectItem>
                    <SelectItem value="sticky">Sticky</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
          
          {/* Styling Properties */}
          <div>
            <div className="flex items-center gap-2 mb-3">
              <Palette className="w-4 h-4 text-gray-600" />
              <h3 className="text-sm font-medium text-gray-700">Styling</h3>
            </div>
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Background Color
                </label>
                <Select 
                  defaultValue="transparent"
                  onValueChange={(value) => handlePropertyChange("backgroundColor", value)}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="transparent">Transparent</SelectItem>
                    <SelectItem value="white">White</SelectItem>
                    <SelectItem value="gray-50">Gray 50</SelectItem>
                    <SelectItem value="gray-100">Gray 100</SelectItem>
                    <SelectItem value="gray-200">Gray 200</SelectItem>
                    <SelectItem value="blue-50">Blue 50</SelectItem>
                    <SelectItem value="blue-100">Blue 100</SelectItem>
                    <SelectItem value="green-50">Green 50</SelectItem>
                    <SelectItem value="red-50">Red 50</SelectItem>
                    <SelectItem value="custom">Custom</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Border Radius
                </label>
                <Select 
                  defaultValue="md"
                  onValueChange={(value) => handlePropertyChange("borderRadius", value)}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">None</SelectItem>
                    <SelectItem value="sm">Small (2px)</SelectItem>
                    <SelectItem value="md">Medium (6px)</SelectItem>
                    <SelectItem value="lg">Large (8px)</SelectItem>
                    <SelectItem value="xl">Extra Large (12px)</SelectItem>
                    <SelectItem value="full">Full (50%)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Shadow
                </label>
                <Select 
                  defaultValue="none"
                  onValueChange={(value) => handlePropertyChange("shadow", value)}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">None</SelectItem>
                    <SelectItem value="sm">Small</SelectItem>
                    <SelectItem value="md">Medium</SelectItem>
                    <SelectItem value="lg">Large</SelectItem>
                    <SelectItem value="xl">Extra Large</SelectItem>
                    <SelectItem value="2xl">2X Large</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Border
                </label>
                <Select 
                  defaultValue="none"
                  onValueChange={(value) => handlePropertyChange("border", value)}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">None</SelectItem>
                    <SelectItem value="1">1px Solid</SelectItem>
                    <SelectItem value="2">2px Solid</SelectItem>
                    <SelectItem value="dashed">Dashed</SelectItem>
                    <SelectItem value="dotted">Dotted</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
          
          {/* Typography Properties (for text components) */}
          {(selectedComponent === "button" || selectedComponent === "input") && (
            <div>
              <div className="flex items-center gap-2 mb-3">
                <Type className="w-4 h-4 text-gray-600" />
                <h3 className="text-sm font-medium text-gray-700">Typography</h3>
              </div>
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Font Size
                  </label>
                  <Select 
                    defaultValue="base"
                    onValueChange={(value) => handlePropertyChange("fontSize", value)}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="xs">Extra Small</SelectItem>
                      <SelectItem value="sm">Small</SelectItem>
                      <SelectItem value="base">Base</SelectItem>
                      <SelectItem value="lg">Large</SelectItem>
                      <SelectItem value="xl">Extra Large</SelectItem>
                      <SelectItem value="2xl">2X Large</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Font Weight
                  </label>
                  <Select 
                    defaultValue="normal"
                    onValueChange={(value) => handlePropertyChange("fontWeight", value)}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="light">Light</SelectItem>
                      <SelectItem value="normal">Normal</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="semibold">Semibold</SelectItem>
                      <SelectItem value="bold">Bold</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </aside>
  );
}
